import { IAudienceDetail } from '@/types/audience';

export type IFbCurrentPage = {
  id: string;
  name: string;
  access_token: string;
};

interface TBusiness {
  id: string;
  name: string;
}

export interface IFbPage {
  id: string;
  account_id: string;
  name: string;
  account_status: number;
  is_accepted_term: boolean;
  business: TBusiness | '';
  link_accept_tos: string;
  selected: boolean;
  account_status_name: string;
}

export interface TFilterAudience {
  search: string;
  date_created_from: string;
  date_created_to: string;
  page: number;
  limit: number;
  platform: 'FB' | 'TT' | 'GG';
}

export interface TFilterCampaign {
  search: string;
  status: '' | 'ACTIVE' | 'PAUSED' | 'DELETED' | 'ARCHIVED';
  date_preset?:
    | ''
    | 'today'
    | 'yesterday'
    | 'last_3d'
    | 'last_7d'
    | 'last_14d'
    | 'last_28d'
    | 'last_30d'
    | 'this_month'
    | 'last_month'
    | 'maximum';
  page: number;
  limit: number;
  since?: string;
  until?: string;
}

export type TFacebookLogResponse = {
  data: TFacebookLogData;
  message: string;
  status: number;
};

export type TFacebookLogData = {
  items: IAudienceDetail[];
  limit: number;
  skip: number;
  total: number;
}


export interface IFacebookUser {
  ad_account_default: null;
  ad_accounts: string[];
  avatar_url: string;
  core_user_id: string;
  name: string;
  scope: number[];
  user_id: number;
}

export interface IAdsAccount {
  ad_account_id: string;
  ad_account_name: string;
}

export interface ICustomAudienceResponse {
  items: IAudienceDetail[];
  count: number;
}

export interface IFacebookHistoryItem {
  job_id: number;
  user_id: number;
  segment_id: 0;
  status: 'COMPLETED' | 'FAILED' | 'PENDING' | 'PROCESSING';
  previous_total_records: number;
  workflow_id: string;
  airflow_dag_run_id: string;
  id: number;
  records: number;
  error_message: string;
  date_completed: string;
  retry_count: number;
  is_initial: boolean;
  segment_info: {
    name: string;
    color: string;
  };
}

export interface IFacebookHistoryResponse {
  items: IFacebookHistoryItem[];
  count: number;
}
