import { APIConfig } from '.';
import { ENDPOINTS } from './endpoints';

const getFbOauthUrl = async (redirect_uri: string) => {
  const res = await APIConfig().post(
    ENDPOINTS.fb_Oauth[''],
    {},
    {
      withCredentials: true,
      params: { redirect_uri },
    },
  );
  return res.data;
};

const getFbOauthCallback = async () => {
  const res = await APIConfig().get(ENDPOINTS.fb_Oauth.callback);
  return res.data;
};
export const getFbAdAccount = async ({ signal }: { signal: AbortSignal }) => {
  const res = await APIConfig().get(ENDPOINTS.fb_Oauth.ad_account, {
    signal,
  });
  return res.data;
};

export const getFacebookLog = async ({ signal, params }: { signal: AbortSignal, params: { [key: string]: unknown } }) => {
  const res = await APIConfig().get(ENDPOINTS.fb.log, {
    signal,
    params
  });
  return res.data;
};

export const removeFbAdAccount = async () => {
  const res = await APIConfig().post(ENDPOINTS.fb_Oauth.remove_account);
  return res.data;
};

const getFbPage = async () => {
  const res = await APIConfig().get(ENDPOINTS.fb_Oauth.page);
  return res.data;
};

const selectFbAdAccount = async (payload: { ad_account_id: string }) => {
  const res = await APIConfig().post(ENDPOINTS.fb_Oauth.ad_account, payload);
  return res.data;
};

const logout = async () => {
  const res = await APIConfig().patch(ENDPOINTS.fb_Oauth.logout);
  return res.data;
};

const updateAudience = async (payload: { job_id: number; segment_update_id: string }) => {
  const res = await APIConfig().post(ENDPOINTS.google.largeUpdate, payload);
  return res.data;
};

export const fbOauthApi = {
  getFbOauthUrl,
  getFbAdAccount,
  getFbOauthCallback,
  selectFbAdAccount,
  getFbPage,
  logout,
  removeFbAdAccount,
  getFacebookLog,
  updateAudience
};
