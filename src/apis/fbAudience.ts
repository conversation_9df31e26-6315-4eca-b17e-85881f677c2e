import { TCampaign, TCampaignResponse } from '@/types/audience';
import { APIConfig } from '.';
import { ENDPOINTS } from './endpoints';
import { AxiosResponse } from 'axios';
import { TFacebookLogData, TFacebookLogResponse } from '@/types/facebook';

const getAudience = async (
  payload: {
    page?: number;
    limit?: number;
    search?: string;
  },
  signal?: AbortSignal,
): Promise<TFacebookLogData> => {
  const res = await APIConfig().get(ENDPOINTS.fb.log, {
    signal: signal,
    params: {
      ...payload,
      order_by: '-date_created',
    },
  });
  return res.data.data;
};

const getCampaign = async (
  payload: {
    page?: number;
    limit?: number;
    search?: string;
  },
  signal?: AbortSignal,
): Promise<TCampaignResponse> => {
  const res = await APIConfig().get(ENDPOINTS.fb.campaign, {
    signal: signal,
    params: {
      ...payload,
      order_by: '-date_created',
    },
  });
  return res.data;
};

const updateCampaign = async (campaign: Partial<TCampaign> = {}) => {
  const { daily_budget, status, id } = campaign;
  const res = await APIConfig().post(`${ENDPOINTS.fb.campaign}${id}/`, {
    daily_budget,
    status,
  });
  return (res.data as AxiosResponse)?.status === 200;
};

const uploadAudience = async (payload: { audience_name: string; segment_id: string }) => {
  const res = await APIConfig().post(ENDPOINTS.fb.push_custom_audience, payload);
  return res.data;
};

const getLog = async (
  payload: {
    page?: number;
    limit?: number;
    status?: string;
  },
  signal?: AbortSignal,
): Promise<TFacebookLogResponse> => {
  const res = await APIConfig().get(ENDPOINTS.fb.log, {
    signal: signal,
    params: {
      ...payload,
    },
  });
  return res.data.data;
};

export const fbAudienceApi = {
  getAudience,
  uploadAudience,
  getCampaign,
  updateCampaign,
  getLog,
};
