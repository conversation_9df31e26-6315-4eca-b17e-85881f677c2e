import Modal from '@/components/Modal';
import { Button } from '@/components/ui/button';
import { RiAddCircleLine } from '@remixicon/react';
import { t } from 'i18next';
import AddAudienceView from './AddAudienceView';

interface IAddAudiencePopup {
  onSubmitAddSegment: (payload: { audience_name: string; segment_id: string }) => void;
  loading: boolean;
  open: boolean;
  setOpen: (open: boolean) => void;
  social: 'tiktok' | 'google' | 'facebook';
}

const AddAudiencePopup = ({ ...props }: IAddAudiencePopup) => {
  const { onSubmitAddSegment, loading, open, setOpen, social } = props;
  const toggleOpen = () => setOpen(!open);
  return (
    <Modal
      openModal={open}
      onOpenChange={setOpen}
      isCloseIcon={false}
      className="max-w-[650px]"
      trigger={
        <Button className="px-2 py-1" size={'lg'}>
          <RiAddCircleLine />
          {t('common.button.addCustomAudience')}
        </Button>
      }
    >
      <AddAudienceView
        toggleOpen={toggleOpen}
        onSubmitAddSegment={onSubmitAddSegment}
        loading={loading}
        social={social}
      />
    </Modal>
  );
};
export default AddAudiencePopup;
