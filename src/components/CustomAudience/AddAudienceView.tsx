import { But<PERSON> } from '@/components/ui/button';
import { useSegmentContext } from '@/pages/context/SegmentContext';
import { RiInformation2Line, RiLoader2Line } from '@remixicon/react';
import { useState } from 'react';
import { cn } from '@/lib/utils';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { useTranslation } from 'react-i18next';
import LabelCustom from '@/components/Label';
import { NoData } from '@/components/NoData';
import { Input } from '@/components/ui/input';
import { useQuery } from '@tanstack/react-query';
import { QUERY_KEY } from '@/utils/constants';
import { get } from '@/apis/apiHelper';
import { ENDPOINTS } from '@/apis/endpoints';
import { useTiktokContext } from '@/pages/TiktokAds/context/TiktokAuthContext';
import { ICountJobs } from '@/types/audience';
import { WarningPushAudience } from '@/components/WarningPushAudience';
import { useGoogleContext } from '@/pages/GoogleAds/context/GoogleAuthContext';
import { upperFirstChar } from '@/utils/helper';

type Props = {
  toggleOpen: () => void;
  onSubmitAddSegment: (payload: { audience_name: string; segment_id: string }) => void;
  loading: boolean;
  social: 'tiktok' | 'google';
};

const AddAudienceView = ({ toggleOpen, onSubmitAddSegment, loading, social }: Props) => {
  const { items } = useSegmentContext();
  const { adsAccount } = useTiktokContext();
  const { adsAccount: adsAccountGoogle } = useGoogleContext();
  const { t } = useTranslation();
  const socialType = upperFirstChar(social, false);
  const [openWarning, setOpenWarning] = useState<boolean>(false);
  const [payload, setPayload] = useState<{
    audience_name: string;
    segment_id: string;
  }>({ audience_name: '', segment_id: '' });

  const newOptions = items.reduce<{ label: string; value: string; count: number }[]>(
    (acc, item) => {
      if (item.contact_quantity > 0) {
        acc.push({
          label: item.name,
          value: item.id,
          count: item.contact_quantity,
        });
      }
      return acc;
    },
    [],
  );
  const segmentSelected = items.find((item) => item.id === payload.segment_id);

  const {
    data: countJobs,
    isLoading: loadingCountJob,
    isFetching: fetchingJob,
    isPending: pendingJob,
  } = useQuery({
    queryKey: [QUERY_KEY.CUSTOM_AUDIENCE_COUNT_TIKTOK, payload.segment_id],
    enabled: !!payload.segment_id,
    queryFn: () => handleGetCountJobs(),
  });

  const handleGetCountJobs = () => {
    const adsAccountId =
      social === 'google'
        ? adsAccountGoogle?.ad_account_id
        : social === 'tiktok'
          ? adsAccount?.ad_account_id
          : '';

    return get<ICountJobs>({
      endpoint: ENDPOINTS.custom_audience.countJobs,
      params: {
        segment_id: payload.segment_id,
        ad_account_id: adsAccountId,
      },
    });
  };

  const countSegmentPushed = countJobs?.data?.data?.count ?? 0;

  const submitCreate = () => {
    onSubmitAddSegment({
      ...payload,
      audience_name: (payload?.audience_name || segmentSelected?.name) ?? '',
    });
  };

  const handleSubmit = () => {
    if (countSegmentPushed > 0) {
      setOpenWarning(true);
    } else {
      submitCreate();
    }
  };

  return (
    <div className="flex flex-col gap-4">
      <div className="flex items-center flex-col">
        <span className="text-lg leading-8 font-medium tracking-[0.6px]">
          {t('audience.addCustomAudience')}
        </span>
        <span className="text-secondary text-sm">
          {t('audience.chooseSegment', { social: socialType })}
        </span>
      </div>
      <div className="flex flex-col">
        <LabelCustom
          isRequire={true}
          className="mb-1"
          label={t('common.facebookAds.audiences.segment')}
        />
        <Select onValueChange={(value) => setPayload((prev) => ({ ...prev, segment_id: value }))}>
          <SelectTrigger className="w-full h-10 rounded-xl">
            <SelectValue placeholder={t('common.facebookAds.audiences.segmentPlaceholder')} />
          </SelectTrigger>
          <SelectContent className="max-h-[250px] overflow-auto p-2 rounded-xl">
            {!!newOptions.length ? (
              newOptions.map((item) => (
                <SelectItem
                  className={cn(
                    'text-sm p-2 rounded-md cursor-pointer',
                    item.value === payload.segment_id &&
                      '!bg-brand text-white hover:text-white focus:text-white',
                  )}
                  key={item.value}
                  value={item.value}
                >
                  <p>
                    {item.label}
                    <span className="text-xs text-big360Color-neutral-500">
                      {' - '}
                      {Number(item.count).toLocaleString()} {t('common.contacts')}
                    </span>
                  </p>
                </SelectItem>
              ))
            ) : (
              <NoData />
            )}
          </SelectContent>
        </Select>
      </div>
      <div className="flex flex-col">
        <LabelCustom className="mb-1" label={t('audience.customAudienceName')} />
        <Input
          value={payload.audience_name ?? ''}
          onChange={(e) =>
            setPayload((prev) => ({ ...prev, audience_name: (e.target as HTMLInputElement).value }))
          }
          className={cn('outline-none border h-10 w-full p-3 rounded-xl text-sm')}
          placeholder={t('audience.enterCustomAudienceName')}
        />
        <div className="flex items-center gap-1 text-xs text-secondary mt-2">
          <RiInformation2Line size={16} />
          {t('tiktokAds.audiences.notice', {
            segmentName: segmentSelected?.name ?? 'Segment Name',
          })}
        </div>
      </div>
      <div className="flex items-end justify-end gap-3">
        <Button
          onClick={toggleOpen}
          className="px-3 py-1 rounded-xl"
          variant={'outline'}
          size={'lg'}
        >
          {t('common.button.cancel')}
        </Button>
        <Button
          onClick={handleSubmit}
          disabled={loadingCountJob || fetchingJob || pendingJob || !segmentSelected?.id}
          className="px-3 py-1 rounded-xl min-w-[144px]"
          size={'lg'}
        >
          {loading || loadingCountJob || fetchingJob ? (
            <RiLoader2Line className="animate-spin" />
          ) : (
            t('audience.pushToSocial', { social: socialType })
          )}
        </Button>
      </div>
      <WarningPushAudience
        open={openWarning}
        setOpen={setOpenWarning}
        count={countSegmentPushed}
        submit={submitCreate}
        social={social}
      />
    </div>
  );
};
export default AddAudienceView;
