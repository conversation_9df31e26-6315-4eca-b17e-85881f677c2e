import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { CircularProgress } from '@/components/ui/circularProgress';
import { Badge } from '@/components/ui/badge';
import { useTranslation } from 'react-i18next';
import { Box } from '@/components/Box';
import { Progress } from '@/components/ui/progress';
import { RiCloseCircleFill, RiExternalLinkLine } from '@remixicon/react';
import React, { useContext, useEffect, useRef, useState } from 'react';
import { get } from '@/apis/apiHelper';
import { ENDPOINTS } from '@/apis/endpoints';
import { generateFBLink } from '@/utils/generateFBLink';
import { IAudienceDetail } from '@/types/audience';
import { generateTiktokLink } from '@/utils/generateTiktokLink';
import { useTiktokContext } from '@/pages/TiktokAds/context/TiktokAuthContext';
import { Button } from '@/components/ui/button';
import Modal from '@/components/Modal';
import { QUERY_KEY } from '@/utils/constants';
import { useQueryClient } from '@tanstack/react-query';
import { LimitUpdateAudience } from '@/components/LimitUpdateAudience';
import { AudienceContext } from '@/pages/Facebook/context/AudienceContext';
import { useFBContext } from '@/pages/Facebook/context/FbAuthContext';
import { TFilterAudience } from '@/types/facebook';
import { useGoogleContext } from '@/pages/GoogleAds/context/GoogleAuthContext';
import { generateGoogleLink } from '@/utils/generateGoogleLink';

interface IDetailAudienceProps {
  detail: IAudienceDetail;
  act: string;
  type: 'FACEBOOK' | 'TIKTOK' | 'GOOGLE';
  historyContent?: React.ReactNode;
  updateContent?: React.ReactNode;
  onOpenHistoryModal?: (detail: IAudienceDetail) => void;
  onOpenUpdateModal?: (detail: IAudienceDetail) => void;
  popoverStates?: Record<string, boolean>;
  setPopoverStates?: (
    states: Record<string, boolean> | ((prev: Record<string, boolean>) => Record<string, boolean>),
  ) => void;
  refreshingRows?: Set<string>;
  setRefreshingRows?: (rows: Set<string> | ((prev: Set<string>) => Set<string>)) => void;
  rowId?: string;
  filterPayload: TFilterAudience;
}

export const DetailAudience = ({ ...props }: IDetailAudienceProps) => {
  const {
    detail,
    act,
    type,
    historyContent,
    updateContent,
    onOpenHistoryModal,
    onOpenUpdateModal,
    popoverStates,
    setPopoverStates,
    setRefreshingRows,
    rowId,
    filterPayload,
  } = props;
  const { t } = useTranslation();
  const { listPages } = useFBContext();
  const { handleGetAudience } = useContext(AudienceContext);
  const queryClient = useQueryClient();
  const currentFbAccount = listPages.find((item) => item.selected);
  const { adsAccount } = useTiktokContext();
  const { adsAccount: adsGoogleAccount } = useGoogleContext();
  const [process, setProcess] = useState<number>(detail.progress);
  const popoverKey = rowId ? `${rowId}-detail-audience` : `detail-audience-${detail.job_id}`;
  const [localPopoverOpen, setLocalPopoverOpen] = useState(false);
  const isPopoverOpen = popoverStates?.[popoverKey] ?? localPopoverOpen;
  const [openWarning, setOpenWarning] = useState<boolean>(false);
  const setIsPopoverOpen = setPopoverStates
    ? (open: boolean) => setPopoverStates((prev) => ({ ...prev, [popoverKey]: open }))
    : setLocalPopoverOpen;

  const [detailAudience, setDetailAudience] = useState<IAudienceDetail>(detail);
  const fetchedJobIds = useRef<Set<number>>(new Set());

  const audience = detailAudience ?? detail;
  const { status } = audience;
  const isEnabledLink = status === 'COMPLETED' || status === 'COMPLETED_WITH_ERRORS';

  useEffect(() => {
    if (detail.job_id !== audience.job_id) {
      fetchedJobIds.current.clear();
      setDetailAudience(detail);
    }
  }, [detail.job_id, audience.job_id, detail]);

  const TriggerComponent = () => {
    if (type === 'TIKTOK' || type === 'GOOGLE') {
      switch (status) {
        case 'FAILED':
          return <RiCloseCircleFill size={24} color={'#F53E3E'} />;
        case 'PROCESSING':
          return <CircularProgress value={process || 90} size={'xl'} animateOnMount={true} />;
        case 'PENDING':
          return <CircularProgress value={0} size={'xl'} animateOnMount={true} />;
        default:
          return <></>;
      }
    } else {
      switch (status) {
        case 'FAILED':
          return <RiCloseCircleFill size={24} color={'#F53E3E'} />;
        case 'PROCESSING':
        case 'PENDING':
          return <CircularProgress value={process} size={'xl'} animateOnMount={true} />;
        default:
          return <></>;
      }
    }
  };

  const handleChangePopoverOpen = (open: boolean) => {
    setIsPopoverOpen(open);
    if (open) {
      if (
        detail.status !== 'COMPLETED' &&
        detail.status !== 'COMPLETED_WITH_ERRORS' &&
        detail.status !== 'FAILED' &&
        !fetchedJobIds.current.has(detail.job_id)
      ) {
        fetchedJobIds.current.add(detail.job_id);
        if (setRefreshingRows && rowId) {
          setRefreshingRows((prev) => new Set(prev).add(rowId));
        }
      }
    } else {
      if (type === 'FACEBOOK' && status !== 'FAILED') {
        handleGetAudience({
          ...filterPayload,
          ad_account_id: currentFbAccount?.id ?? '',
        });
      }
      if (type === 'TIKTOK' && status !== 'FAILED') {
        queryClient.invalidateQueries({
          queryKey: [QUERY_KEY.TIKTOK_AUDIENCE],
        });
      }
      if (type === 'GOOGLE' && status !== 'FAILED') {
        queryClient.invalidateQueries({
          queryKey: [QUERY_KEY.GOOGLE_AUDIENCE],
        });
      }
    }
  };

  const gotoLink = () => {
    switch (type) {
      case 'FACEBOOK':
        return {
          href: generateFBLink(act),
          target: '_blank',
          title: t('common.facebookAds.goToFbAds'),
        };
      case 'TIKTOK':
        return {
          href: generateTiktokLink({
            cid: audience.audience_id,
            aadvid: adsAccount?.ad_account_id ?? '',
          }),
          target: '_blank',
          title: t('tiktokAds.goToTiktokAds'),
        };
      case 'GOOGLE':
        return {
          href: generateGoogleLink({
            cid: audience.audience_id,
            aadvid: adsGoogleAccount?.ad_account_id ?? '',
          }),
          target: '_blank',
          title: t('googleAds.goToGoogleAds'),
        };
      default:
        return { href: '/', target: '_self', title: '' };
    }
  };

  const validateUpdate = () => {
    const updatedAt = new Date(detail.updated_at);
    const currentTime = new Date();
    const timeDifferenceInHours = (currentTime.getTime() - updatedAt.getTime()) / (1000 * 60 * 60);

    if (timeDifferenceInHours < 1) {
      setOpenWarning(true);
      return;
    } else {
      if (onOpenUpdateModal) {
        onOpenUpdateModal(audience);
      }
    }
  };

  return (
    <div className="flex flex-col gap-0.5">
      <div className="flex w-full justify-between items-center gap-2">
        <div className="line-clamp-1 text-sm font-semibold truncate">{audience.audience_name}</div>
        <div className="relative">
          <Popover open={isPopoverOpen} onOpenChange={handleChangePopoverOpen}>
            <PopoverTrigger asChild>
              <div className="cursor-pointer">
                <TriggerComponent />
              </div>
            </PopoverTrigger>
            <PopoverContent className="p-2 min-w-[228px] w-full" align="center">
              <ContentComponent
                type={type}
                jobId={audience.job_id}
                status={status}
                filterPayload={filterPayload}
                setProcess={setProcess}
              />
            </PopoverContent>
          </Popover>
        </div>
      </div>
      <div className="flex gap-2">
        {isEnabledLink && (
          <a
            className="flex items-center gap-1 text-[#2C9EFF] relative w-fit text-xs"
            href={gotoLink().href}
            target={gotoLink().target}
          >
            <div className="relative">
              {gotoLink().title}
              <div className="h-[1px] absolute bottom-0 w-full right-0 bg-[#2C9EFF]" />
            </div>
            <RiExternalLinkLine className="flex-shrink-0" size={16} />
          </a>
        )}
        {(type === 'TIKTOK' || type === 'GOOGLE') && (
          <>
            {status !== 'PROCESSING' && status !== 'FAILED' && (
              <>
                {onOpenUpdateModal ? (
                  <Button
                    variant={'linkText'}
                    className="w-fit py-0 p-0 m-0 h-auto"
                    onClick={validateUpdate}
                    disabled={status === 'PENDING'}
                  >
                    {t('common.update')}
                  </Button>
                ) : (
                  updateContent
                )}
              </>
            )}

            {/* Use callback if available, otherwise render modal */}
            {onOpenHistoryModal ? (
              <Button
                variant={'ghost'}
                className="w-fit py-0 p-0 m-0 h-auto"
                onClick={() => onOpenHistoryModal(audience)}
              >
                {t('common.history')}
              </Button>
            ) : (
              <Modal
                trigger={
                  <Button variant={'ghost'} className="w-fit py-0 p-0 m-0 h-auto">
                    {t('common.history')}
                  </Button>
                }
                className="max-w-[920px] w-full h-[592px]"
                title={
                  <div className="h-[40px]">
                    <p className="text-lg font-semibold text-big360Color-neutral-950">
                      {t('common.updateHistory')}
                    </p>
                    <p className="text-sm font-normal text-big360Color-neutral-700">
                      {t('common.customAudienceName')}
                    </p>
                  </div>
                }
              >
                {historyContent}
              </Modal>
            )}
          </>
        )}
      </div>
      <LimitUpdateAudience open={openWarning} setOpen={setOpenWarning} />
    </div>
  );
};

interface IContentComponentProps {
  type: 'FACEBOOK' | 'TIKTOK' | 'GOOGLE';
  jobId: number;
  status: string;
  setProcess: (process: number) => void;
  filterPayload?: TFilterAudience;
}

const ContentComponent: React.FC<IContentComponentProps> = ({
  type,
  jobId,
  status,
  setProcess,
}: IContentComponentProps) => {
  const { t } = useTranslation();
  const [detailAudience, setDetailAudience] = useState<IAudienceDetail>();
  const [isLoadingDetail, setIsLoadingDetail] = useState(false);
  useEffect(() => {
    if (status !== 'FAILED') {
      const handleFetchDetail = () => {
        if (isLoadingDetail) {
          return;
        } // Prevent duplicate calls

        setIsLoadingDetail(true);

        switch (type) {
          case 'FACEBOOK':
            get({
              endpoint: ENDPOINTS.fb.log_detail(jobId),
            })
              .then((res) => {
                const dataAudience = res?.data?.data as unknown as IAudienceDetail;
                setDetailAudience(dataAudience);
                setProcess(dataAudience.progress || 0); // Update process state
                // Remove from refreshing state when done
              })
              .catch((error) => {
                console.error('Error fetching Facebook audience detail:', error);
              })
              .finally(() => {
                setIsLoadingDetail(false);
              });
            break;
          case 'TIKTOK':
          case 'GOOGLE':
            get({
              endpoint: ENDPOINTS.custom_audience.detail(jobId.toString()),
            })
              .then((res) => {
                const dataAudience = res?.data?.data as unknown as IAudienceDetail;
                setDetailAudience(dataAudience);
                const percentage = detailAudience?.total_records
                  ? ((detailAudience?.total_records * 0.9) / detailAudience?.total_records) * 100
                  : 0;
                setProcess(percentage);
              })
              .finally(() => {
                setIsLoadingDetail(false);
              });
            break;
          default:
            setIsLoadingDetail(false);
            break;
        }
      };

      handleFetchDetail();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [status]);

  const handleConvertStatus = (progress: number) => {
    const statusAudience = detailAudience?.status;
    if (type === 'TIKTOK' || type === 'GOOGLE') {
      switch (statusAudience) {
        case 'FAILED':
          return <Badge variant={'error'}>{t('common.failed')}</Badge>;
        case 'COMPLETED':
        case 'COMPLETED_WITH_ERRORS':
          return <Badge variant={'success'}>{t('common.success')}</Badge>;
        case 'PROCESSING':
          return (
            <Badge variant={'process'}>
              {t('common.inprogress')} {`${90}%`}
            </Badge>
          );
        case 'PENDING':
          return <Badge variant={'secondary'}>{t('common.pending')}</Badge>;
      }
    } else {
      switch (statusAudience) {
        case 'FAILED':
          return <Badge variant={'error'}>{t('common.failed')}</Badge>;
        case 'COMPLETED':
        case 'COMPLETED_WITH_ERRORS':
          return <Badge variant={'success'}>{t('common.success')}</Badge>;
        case 'PROCESSING':
          return (
            <Badge variant={'process'}>
              {t('common.inprogress')} {`${progress}%`}
            </Badge>
          );
        case 'PENDING':
          return <Badge variant={'secondary'}>{t('common.pending')}</Badge>;
      }
    }
  };

  const handleViewCount = () => {
    const statusAudience = detailAudience?.status;
    const totalRecords = detailAudience?.total_records || 0;
    switch (type) {
      case 'TIKTOK':
      case 'GOOGLE':
        if (statusAudience !== 'COMPLETED' && statusAudience !== 'COMPLETED_WITH_ERRORS') {
          return totalRecords ? totalRecords * 0.9 : 0;
        } else {
          return totalRecords || 0;
        }
      case 'FACEBOOK':
        return detailAudience?.processed_records || 0;
      default:
        return 0;
    }
  };

  const handleViewProcess = () => {
    const statusAudience = detailAudience?.status;
    const totalRecords = detailAudience?.total_records || 0;
    switch (type) {
      case 'TIKTOK':
      case 'GOOGLE':
        if (statusAudience !== 'COMPLETED' && statusAudience !== 'COMPLETED_WITH_ERRORS') {
          return totalRecords ? Math.round((handleViewCount() / totalRecords) * 100) : 0;
        } else {
          return 100;
        }
      case 'FACEBOOK':
        return detailAudience?.progress;
      default:
        return 0;
    }
  };

  return (
    <>
      <Box className="gap-5">
        <div className="text-xs">{handleConvertStatus(detailAudience?.progress || 0)}</div>
        <p className="text-xs text-secondary">
          {Number(handleViewCount()).toLocaleString()}/
          {Number(detailAudience?.total_records || 0).toLocaleString()} {t('common.contacts')}
        </p>
      </Box>
      <div className="bg-[#F0F0F0] rounded-full mt-2">
        <Progress value={handleViewProcess()} className="h-[8px]" color={'#515667'} />
      </div>
    </>
  );
};
