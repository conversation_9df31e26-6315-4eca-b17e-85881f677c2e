export const facebookAds = {
  title: 'Facebook Ads',
  selectAccount: 'Select Facebook Ads account',
  switchAccount: 'Switch Account',

  description: 'Create and manage custom audiences for your Facebook advertising campaigns.',

  pushToTiktokAds:'Push to Facebook',

  //   nodata
  nodata: 'You haven’t linked a Facebook account yet.',
  nodataDescription:
    'Connect your Facebook account to start publishing content, building Custom Audiences, generate reports and more ...',
  connect: 'Connect Facebook',

  // select advertiser
  selectAdvertiser: 'Select advertiser',
  selectAdvertiserDescription:
    'Select your ad account that you would like to set default to CRM360.',
  selectAdvertiserNotice: 'Only available when connected to a Facebook Business Account.',

  //   create custom audience
  createCustomAudience: 'Create Custom Audience',
  createCustomAudienceDescription:
    ' Choose a Segment to create a Custom Audience for your Facebook Ads account',
  createCustomAudienceTooltip:
    'The Custom Audience name defaults to the Segment name if not entered.',

  //   disconnect
  disconnectTitle: 'Disconnect Facebook Ads account?',
  disconnectDescription:
    'This will remove your Facebook Ads connection from Big360.\nYou won’t be able to publish, sync audiences, or view reports until you reconnect.',

  account: {
    title: 'Select Facebook Ads Account',
    description: 'Select your ad account that you would like to set default to CRM360.',
  },

  pickADate: 'Pick a date',

  goToTiktokAds: 'Go to Facebook Ads',

  audiences: {
    notice: 'If left blank, the Custom Audience name will default to "{{segmentName}}"',
  },

  accessDenied: 'Access Denied',
  accessDeniedDescription:
    "You don't have the required permissions to access Facebook Audience Management. Please contact your Facebook Ads account administrator to grant you the necessary permissions.",

  requiredPermission: 'Required Permissions:',
  audienceManagement: 'Audience Management',
  reConnect: 'Re-connect',

  somethingWentWrong: 'Something went wrong',
  somethingWentWrongDescription:
    "We're experiencing technical difficulties connecting to Facebook Ads. This could be due to a temporary service outage or network issue.",

  createAnyway: 'Create anyway',

  existsSegment: 'This segment already exists',
  updateFrequency: 'Update frequency restriction'
};
