import { useState } from 'react';
import DataTable from '@/components/table/DataTable';
import { TFilterAudience } from '@/types/facebook';
import { ICustomAudienceResponse } from '@/types/tiktok';
import FilterPanel from '@/pages/TiktokAds/components/FilterPanel';
import audienceCol from '@/pages/TiktokAds/components/Column/audienceCol';
import { useTiktokContext } from '@/pages/TiktokAds/context/TiktokAuthContext';
import { formatDateYYYYMMDD } from '@/utils/helper';
import { IAudienceDetail } from '@/types/audience';

interface ICustomAudiences {
  filterPayload: TFilterAudience;
  setFilterPayload: (value: TFilterAudience) => void;
  loading?: boolean;
  tiktokCustomAudience: ICustomAudienceResponse;
  onOpenHistoryModal: (detail: IAudienceDetail) => void;
  onOpenUpdateModal: (detail: IAudienceDetail) => void;
}

const CustomTiktokAudiences = ({
  filterPayload,
  setFilterPayload,
  loading,
  tiktokCustomAudience,
  onOpenHistoryModal,
  onOpenUpdateModal,
}: ICustomAudiences) => {
  const { adsAccount } = useTiktokContext();
  const [popoverStates, setPopoverStates] = useState<Record<string, boolean>>({});
  const [refreshingRows, setRefreshingRows] = useState<Set<string>>(new Set());

  return (
    <div className="flex flex-col">
      <FilterPanel
        setFilterPayload={(value) => {
          setFilterPayload({
            ...filterPayload,
            ...value,
            date_created_from: formatDateYYYYMMDD(value?.date_created_from ?? '', '-'),
            date_created_to: formatDateYYYYMMDD(value?.date_created_to ?? '', '-'),
          });
        }}
        filterPayload={filterPayload}
      />
      <DataTable
        className="h-[468px]"
        data={tiktokCustomAudience.items}
        columns={audienceCol({
          act: adsAccount?.ad_account_id || '',
          onOpenHistoryModal,
          onOpenUpdateModal,
          filterPayload,
        })}
        total={tiktokCustomAudience.count}
        loading={loading}
        pagination={{ pageSize: filterPayload.limit, currentPage: filterPayload.page }}
        setPagination={(value) => {
          setFilterPayload({ ...filterPayload, limit: value.pageSize, page: value.currentPage });
        }}
        popoverStates={popoverStates}
        setPopoverStates={setPopoverStates}
        refreshingRows={refreshingRows}
        setRefreshingRows={setRefreshingRows}
      />
    </div>
  );
};
export default CustomTiktokAudiences;
