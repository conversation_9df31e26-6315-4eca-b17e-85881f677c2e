import React, { useState } from 'react';
import Navbar from './Navbar';
import { TAB_FACEBOOK } from '@/constants/facebook';
import CustomAudiences from '../views/CustomAudiences';
import Campaign from '../views/Campaign';
import { CampaignProvider } from '@/pages/Facebook/context/CampaignContext';
import { TFilterAudience } from '@/types/facebook';
import { useFBContext } from '@/pages/Facebook/context/FbAuthContext';
import HeaderFacebook from '@/pages/Facebook/components/HeaderFacebook';

const Container = () => {
  const [tab, setTab] = useState<TAB_FACEBOOK>(TAB_FACEBOOK.AUDIENCES);
  const [filterPayload, setFilterPayload] = useState<TFilterAudience>({
    search: '',
    page: 1,
    limit: 10,
    date_created_from: '',
    date_created_to: '',
    platform: 'FB',
  });
  const { listPages } = useFBContext();
  const currentFbAccount = listPages.find((item) => item.selected);

  const renderView = () => {
    switch (tab) {
      case TAB_FACEBOOK.AUDIENCES:
        return (
          <CustomAudiences
            filterPayload={filterPayload}
            setFilterPayload={setFilterPayload}
            listPages={listPages}
          />
        );
      case TAB_FACEBOOK.CAMPAIGN:
        return <Campaign />;
      default:
        return null;
    }
  };

  const getProvider = (children: React.ReactNode) => {
    if (tab === TAB_FACEBOOK.AUDIENCES) {
      return children;
    }
    if (tab === TAB_FACEBOOK.CAMPAIGN) {
      return <CampaignProvider>{children}</CampaignProvider>;
    }
    return children;
  };

  return getProvider(
    <>
      <HeaderFacebook setFilterPayload={setFilterPayload} ad_Account_id={currentFbAccount?.id ?? ''} />
      <Navbar path={tab} setPath={setTab} />
      {renderView()}
    </>,
  );
};

export default Container;
