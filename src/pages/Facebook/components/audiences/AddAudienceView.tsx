import { fb<PERSON><PERSON><PERSON><PERSON><PERSON> } from '@/apis/fbAudience';
import { Button } from '@/components/ui/button';
import { toast } from '@/hooks/use-toast';
import { useSegmentContext } from '@/pages/context/SegmentContext';
import { RiInformation2Line, RiLoader2Line } from '@remixicon/react';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { useState } from 'react';
import { cn } from '@/lib/utils';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { TFilterAudience } from '@/types/facebook';
import { get } from '@/apis/apiHelper';
import { ICountJobs } from '@/types/audience';
import { ENDPOINTS } from '@/apis/endpoints';
import { QUERY_KEY } from '@/utils/constants';
import { WarningPushAudience } from '@/components/WarningPushAudience';
import { useTranslation } from 'react-i18next';
import { NoData } from '@/components/NoData';

type Props = {
  toggleOpen: () => void;
  setFilterPayload?: (value: TFilterAudience) => void;
  ad_Account_id?: string;
};

const AddAudienceView = ({ toggleOpen, setFilterPayload, ad_Account_id }: Props) => {
  const { items } = useSegmentContext();
  const queryClient = useQueryClient();

  const { t } = useTranslation();
  const [openWarning, setOpenWarning] = useState<boolean>(false);
  const [payload, setPayload] = useState<{
    audience_name: string;
    segment_id: string;
  }>({ audience_name: '', segment_id: '' });

  const newOptions = items.reduce<{ label: string; value: string }[]>((acc, item) => {
    if (item.contact_quantity > 0) {
      acc.push({
        label: item.name,
        value: item.id,
        count: item.contact_quantity,
      });
    }
    return acc;
  }, []);
  const segmentSelected = items.find((item) => item.id === payload.segment_id);

  const mutation = useMutation({
    mutationFn: fbAudienceApi.uploadAudience,
    onSuccess: () => {
      toggleOpen();
      queryClient.invalidateQueries({
        queryKey: [QUERY_KEY.FACEBOOK_AUDIENCE],
      });
      if (!!setFilterPayload) {
        setFilterPayload({
          date_created_from: '',
          date_created_to: '',
          limit: 10,
          page: 1,
          search: '',
          platform: 'FB',
        });
      }

      toast({
        title: t('audience.addCustomAudienceSuccess'),
        status: 'success',
      });
    },
    onError: (e) => e,
  });
  const onSubmitAddSegment = async () => {
    if (!segmentSelected) {
      return;
    }
    mutation.mutate({
      ...payload,
      audience_name: (payload?.audience_name || segmentSelected?.name) ?? '',
    });
  };

  const {
    data: countJobs,
    isLoading: loadingCountJob,
    isFetching: fetchingJob,
    isPending: pendingJob,
  } = useQuery({
    queryKey: [QUERY_KEY.CUSTOM_AUDIENCE_COUNT_FACEBOOK, payload.segment_id],
    enabled: !!payload.segment_id,
    queryFn: () => handleGetCountJobs(),
  });

  const handleGetCountJobs = () => {
    return get<ICountJobs>({
      endpoint: ENDPOINTS.custom_audience.countJobs,
      params: {
        segment_id: payload.segment_id,
        ad_account_id: ad_Account_id,
      },
    });
  };

  const countSegmentPushed = countJobs?.data?.data?.count ?? 0;

  const handleSubmit = () => {
    if (countSegmentPushed > 0) {
      setOpenWarning(true);
    } else {
      onSubmitAddSegment().finally();
    }
  };

  return (
    <>
      <div className="flex flex-col gap-4">
        <div className="flex items-center flex-col">
          <span className="text-lg leading-8 font-medium tracking-[0.6px]">
            {t('common.facebookAds.audiences.addCustomAudience')}
          </span>
          <span className="text-secondary text-sm">
            {t('common.facebookAds.audiences.chooseSegment')}
          </span>
        </div>
        <div className="flex flex-col">
          <Label
            isRequire={true}
            className="mb-1"
            label={t('common.facebookAds.audiences.segment')}
          />
          <Select onValueChange={(value) => setPayload((prev) => ({ ...prev, segment_id: value }))}>
            <SelectTrigger className="w-full h-10 rounded-xl">
              <SelectValue placeholder={t('common.facebookAds.audiences.segmentPlaceholder')} />
            </SelectTrigger>
            <SelectContent className="max-h-[250px] overflow-auto p-2 rounded-xl">
              {!!newOptions.length ? (
                newOptions.map((item) => (
                  <SelectItem
                    className={cn(
                      'text-sm p-2 rounded-md cursor-pointer',
                      item.value === payload.segment_id &&
                      '!bg-brand text-white hover:text-white focus:text-white',
                    )}
                    key={item.value}
                    value={item.value}
                  >
                    <p>
                      {item.label}
                      <span className="text-xs text-big360Color-neutral-500">
                      {' - '}
                        {Number(item.count).toLocaleString()} {t('common.contacts')}
                    </span>
                    </p>
                  </SelectItem>
                ))
              ) : (
                <NoData />
              )}
            </SelectContent>
          </Select>
          {payload.segment_id && (
            <div className="flex items-center gap-1 mt-2">
              <span className="text-sm text-secondary">
                {t('common.facebookAds.totalContact')}:
              </span>
              <span className="font-medium">
                {Number(segmentSelected?.contact_quantity).toLocaleString()}
              </span>
            </div>
          )}
        </div>
        <div className="flex flex-col">
          <Label className="mb-1" label={t('common.facebookAds.audiences.name')} />
          <input
            value={payload.audience_name}
            onChange={(e) => setPayload((prev) => ({ ...prev, audience_name: e.target.value }))}
            className={cn('outline-none border h-10 w-full p-3 rounded-xl text-sm')}
            placeholder={t('common.facebookAds.enterAudienceName')}
          />
          <div className="flex items-center gap-1 text-xs text-secondary mt-2">
            <RiInformation2Line size={16} />
            {t('common.facebookAds.audiences.notice')}
          </div>
        </div>
        <div className="flex items-end justify-end gap-3">
          <Button
            onClick={toggleOpen}
            className="px-3 py-1 rounded-xl"
            variant={'secondary'}
            size={'lg'}
          >
            {t('common.button.cancel')}
          </Button>
          <Button
            onClick={handleSubmit}
            disabled={loadingCountJob || fetchingJob || pendingJob || !segmentSelected?.id}
            className="px-3 py-1 rounded-xl min-w-[144px]"
            size={'lg'}
          >
            {mutation.isPending || loadingCountJob || fetchingJob ? (
              <RiLoader2Line className="animate-spin" />
            ) : (
              t('common.facebookAds.audiences.pushToFb')
            )}
          </Button>
        </div>
      </div>
      <WarningPushAudience
        open={openWarning}
        setOpen={setOpenWarning}
        count={countSegmentPushed}
        submit={onSubmitAddSegment}
        social={'facebook'}
      />
    </>
  );
};
export default AddAudienceView;

const Label = ({
  label,
  className,
  isRequire,
}: {
  label: string;
  className?: string;
  isRequire?: boolean;
}) => (
  <span
    className={cn(
      'text-sm font-semibold text-secondary-foreground flex items-center gap-1',
      className,
    )}
  >
    {label}
    {isRequire && <span className="text-destructive">*</span>}
  </span>
);
