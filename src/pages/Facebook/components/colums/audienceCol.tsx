import { formatDateTime } from '@/utils/helper';
import { ColumnDef } from '@tanstack/react-table';
import { t } from 'i18next';
import { DetailAudience } from '@/components/DetailAudience';
import { IAudienceDetail } from '@/types/audience';
import { TFilterAudience } from '@/types/facebook';

type Props = {
  act: string;
  filterPayload: TFilterAudience
};

const audienceCol = ({ act, filterPayload }: Props): ColumnDef<IAudienceDetail>[] => {
  return [
    {
      accessorKey: 'audience_name',
      header: () => t('common.facebookAds.customAudience'),
      cell: ({ row, ...context }) => {
        return (
          <DetailAudience
            detail={row.original}
            act={act}
            type={'FACEBOOK'}
            /* eslint-disable @typescript-eslint/no-explicit-any */
            popoverStates={(context as any).popoverStates}
            setPopoverStates={(context as any).setPopoverStates}
            refreshingRows={(context as any).refreshingRows}
            setRefreshingRows={(context as any).setRefreshingRows}
            rowId={(context as any).rowId}
            filterPayload={filterPayload}
          />
        );
      },
      size: 200,
    },
    {
      accessorKey: 'segment.name',
      header: () => t('segment.title'),
      cell: ({ row }) => {
        return (
          <div className="flex items-center gap-2">
            <div className="flex-shrink-0">
              {/*{row.original.segment?.color && <TagFillIcon color={row.original.segment?.color} />}*/}
            </div>
            <span className="line-clamp-1">{row.original.segment_name}</span>
          </div>
        );
      },
      size: 366,
    },
    {
      accessorKey: 'number_contact',
      header: () => <div className="text-center w-full">{t('segment.totalContacts')}</div>,
      cell: ({ row }) => {
        return (
          <div className="text-center w-full">
            {Number(row.original.total_records).toLocaleString()}
          </div>
        );
      },
      size: 200,
    },
    {
      accessorKey: 'date_created',
      header: () => <div className="text-center w-full">{t('common.facebookAds.importDate')}</div>,
      cell: ({ row }) => {
        return (
          <div className="text-center">{formatDateTime(row.original.created_at, '/', ':')}</div>
        );
      },
      size: 200,
    },
  ];
};

export default audienceCol;
