import { useContext, useEffect, useState } from 'react';
import FilterPanel from '../components/FilterPanel';
import DataTable from '@/components/table/DataTable';
import audienceCol from '../components/colums/audienceCol';
import { AudienceContext } from '../context/AudienceContext';
import { IFbPage, TFilterAudience, TFilterAudienceTiktok } from '@/types/facebook';
import { IAudienceDetail } from '@/types/audience';

interface ICustomAudiences {
  filterPayload: TFilterAudienceTiktok;
  setFilterPayload: (value: TFilterAudienceTiktok) => void;
  listPages: IFbPage[];
}

const CustomAudiences = ({ filterPayload, setFilterPayload, listPages }: ICustomAudiences) => {
  const [ad_Account_id, setAd_Account_id] = useState<string>('');
  const { items, count, loading, handleGetAudience } = useContext(AudienceContext);
  const [listAudiences, setListAudiences] = useState<IAudienceDetail[]>(items);

  // Popover state management
  const [popoverStates, setPopoverStates] = useState<Record<string, boolean>>({});
  const [refreshingRows, setRefreshingRows] = useState<Set<string>>(new Set());

  const currentFbAccount = listPages.find((item) => item.selected);

  useEffect(() => {
    if (!!items.length) {
      setListAudiences(items);
    }
  }, [items]);

  useEffect(() => {
    if (!ad_Account_id) {
      return;
    }
    handleGetAudience({
      ...filterPayload,
      ad_account_id: ad_Account_id,
    });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [filterPayload, ad_Account_id]);

  useEffect(() => {
    if (!listPages || !currentFbAccount) {
      return;
    }
    setAd_Account_id(currentFbAccount.id);
  }, [currentFbAccount, listPages]);

  return (
    <div className="flex flex-col">
      <FilterPanel
        setFilterPayload={(value) => {
          setFilterPayload({ ...filterPayload, ...value });
        }}
        filterPayload={filterPayload}
      />
      <DataTable
        className="h-[468px]"
        data={listAudiences}
        columns={audienceCol({
          act: currentFbAccount?.account_id || '',
          filterPayload,
        })}
        total={count}
        loading={loading}
        pagination={{ pageSize: filterPayload.limit, currentPage: filterPayload.page }}
        setPagination={(value) => {
          setFilterPayload({ ...filterPayload, limit: value.pageSize, page: value.currentPage });
        }}
        popoverStates={popoverStates}
        setPopoverStates={setPopoverStates}
        refreshingRows={refreshingRows}
        setRefreshingRows={setRefreshingRows}
      />
    </div>
  );
};
export default CustomAudiences;
