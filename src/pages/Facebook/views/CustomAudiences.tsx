import { useEffect, useState } from 'react';
import FilterPanel from '../components/FilterPanel';
import DataTable from '@/components/table/DataTable';
import audienceCol from '../components/colums/audienceCol';
import { ICustomAudienceResponse, IFbPage, TFilterAudience } from '@/types/facebook';
import { ENDPOINTS } from '@/apis/endpoints';
import { QUERY_KEY } from '@/utils/constants';
import { get } from '@/apis/apiHelper';
import { useQuery, useQueryClient } from '@tanstack/react-query';
import Modal from '@/components/Modal';
import { useTranslation } from 'react-i18next';
import { IAudienceDetail } from '@/types/audience';
import HistoryFacebook from '@/pages/Facebook/components/HistoryFacebook';
import UpdateCustomAudience from '@/pages/Facebook/components/UpdateCustomAudience';

interface ICustomAudiences {
  filterPayload: TFilterAudience;
  setFilterPayload: (value: TFilterAudience) => void;
  listPages: IFbPage[];
}

const CustomAudiences = ({ filterPayload, setFilterPayload, listPages }: ICustomAudiences) => {
  const [ad_Account_id, setAd_Account_id] = useState<string>('');
  const { t } = useTranslation();
  const queryClient = useQueryClient();
  const [facebookCustomAudience, setFacebookCustomAudience] = useState<ICustomAudienceResponse>({
    items: [],
    count: 0,
  });
  // Popover state management
  const [popoverStates, setPopoverStates] = useState<Record<string, boolean>>({});
  const [refreshingRows, setRefreshingRows] = useState<Set<string>>(new Set());
  const [historyModal, setHistoryModal] = useState<{
    isOpen: boolean;
    audienceDetail: IAudienceDetail | null;
  }>({
    isOpen: false,
    audienceDetail: null,
  });

  const [updateModal, setUpdateModal] = useState<{
    isOpen: boolean;
    audienceDetail: IAudienceDetail | null;
  }>({
    isOpen: false,
    audienceDetail: null,
  });
  const currentFbAccount = listPages.find((item) => item.selected);

  const {
    data: customAudienceResponse,
    isLoading: loadingContact,
    isFetching: isFetchingContact,
    isPending: isPendingContact,
  } = useQuery({
    queryKey: [QUERY_KEY.FACEBOOK_AUDIENCE, filterPayload, ad_Account_id],
    enabled: !!ad_Account_id,
    staleTime: 1000,
    queryFn: () =>
      get({
        endpoint: ENDPOINTS.custom_audience[''],
        params: {
          ...filterPayload,
          ad_account_id: ad_Account_id,
          order_by: '-date_created',
        },
      }).then((res) => {
        if (res?.data?.code === 1001) {
          return {
            items: [],
            count: 0,
          };
        }
        return res.data?.data as ICustomAudienceResponse;
      }),
  });

  const openHistoryModal = (detail: IAudienceDetail) => {
    setHistoryModal({
      isOpen: true,
      audienceDetail: detail,
    });
  };

  const closeHistoryModal = () => {
    setHistoryModal({
      isOpen: false,
      audienceDetail: null,
    });
  };

  const openUpdateModal = (detail: IAudienceDetail) => {
    setUpdateModal({
      isOpen: true,
      audienceDetail: detail,
    });
  };

  const closeUpdateModal = () => {
    setUpdateModal({
      isOpen: false,
      audienceDetail: null,
    });
  };

  const handleUpdateAudienceDetail = () => {
    queryClient.invalidateQueries({
      queryKey: [QUERY_KEY.FACEBOOK_AUDIENCE_DETAIL],
    });
  };

  useEffect(() => {
    if (!!customAudienceResponse) {
      setFacebookCustomAudience(customAudienceResponse);
    }
  }, [customAudienceResponse]);

  useEffect(() => {
    if (!listPages || !currentFbAccount) {
      return;
    }
    setAd_Account_id(currentFbAccount.id);
  }, [currentFbAccount, listPages]);

  return (
    <div className="flex flex-col">
      <FilterPanel
        setFilterPayload={(value) => {
          setFilterPayload({ ...filterPayload, ...value });
        }}
        filterPayload={filterPayload}
      />
      <DataTable
        className="h-[468px]"
        data={facebookCustomAudience.items}
        columns={audienceCol({
          act: currentFbAccount?.account_id || '',
          filterPayload,
          onOpenHistoryModal: openHistoryModal,
          onOpenUpdateModal: openUpdateModal,
        })}
        total={facebookCustomAudience.count}
        loading={loadingContact || isFetchingContact || isPendingContact}
        pagination={{ pageSize: filterPayload.limit, currentPage: filterPayload.page }}
        setPagination={(value) => {
          setFilterPayload({ ...filterPayload, limit: value.pageSize, page: value.currentPage });
        }}
        popoverStates={popoverStates}
        setPopoverStates={setPopoverStates}
        refreshingRows={refreshingRows}
        setRefreshingRows={setRefreshingRows}
      />
      {/* History Modal */}
      <Modal
        openModal={historyModal.isOpen}
        onOpenChange={(open) => {
          if (!open) closeHistoryModal();
        }}
        className="max-w-[920px] w-full h-[592px]"
        title={
          <div className="h-[40px]">
            <p className="text-lg font-semibold text-big360Color-neutral-950">
              {t('common.updateHistory')}
            </p>
            <p className="text-sm font-normal text-big360Color-neutral-700">
              {historyModal.audienceDetail?.audience_name || t('common.customAudienceName')}
            </p>
          </div>
        }
      >
        {historyModal.audienceDetail && (
          <HistoryFacebook
            jobId={historyModal.audienceDetail.job_id}
            totalContact={historyModal.audienceDetail.total_records}
            onUpdateAudienceDetail={handleUpdateAudienceDetail}
          />
        )}
      </Modal>

      {/* Update Modal */}
      <Modal
        openModal={updateModal.isOpen}
        onOpenChange={(open) => {
          if (!open) closeUpdateModal();
        }}
        titleAlign={'center'}
        className="max-w-[650px] w-full h-[534px]"
        title={
          <div className="h-[40px]">
            <p className="text-lg font-semibold text-big360Color-neutral-950">
              {t('common.update')} {t('common.facebookAds.customAudience')}
            </p>
            <p className="text-sm font-normal text-big360Color-neutral-700">
              {updateModal.audienceDetail?.audience_name || ''}
            </p>
          </div>
        }
      >
        {updateModal.audienceDetail && (
          <UpdateCustomAudience detail={updateModal.audienceDetail} onClose={closeUpdateModal} />
        )}
      </Modal>
    </div>
  );
};
export default CustomAudiences;
