import { createContext, ReactNode, useContext, useEffect, useRef, useState } from 'react';
import { useMutation } from '@tanstack/react-query';
import { deleteStorage, setStorage } from '@/utils/asyncStorage';
import { IAdsAccount, IFacebookUser, IFbPage } from '@/types/facebook';
import { FACEBOOK_STORAGE_KEY } from '@/constants/facebook';
import { fbOauthApi } from '@/apis/fbOauth';

type InitSate = {
  listPages: IFbPage[];
  adsAccount: IAdsAccount | undefined;
  facebookUser: IFacebookUser | undefined;
  listPagesGrouped: IGroupAccount[];
  isLogin: boolean;
  loading: boolean;
  refetchLoading: boolean;
  updateStateSelected: (id: string) => void;
  logout: () => void;
  handleRefetchListPage: () => void;
  isAccountSelected: boolean;
};

const FbAuthContext = createContext<InitSate>({
  listPages: [],
  adsAccount: undefined,
  facebookUser: undefined,
  listPagesGrouped: [],
  isLogin: false,
  loading: false,
  refetchLoading: false,
  updateStateSelected: () => {},
  logout: () => {},
  handleRefetchListPage: () => {},
  isAccountSelected: false,
});

export interface IGroupAccount {
  business_id: string;
  business_name: string;
  accounts: IFbPage[];
}

export const FbAuthProvider = ({ children }: { children: ReactNode }) => {
  const [listPages, setListPages] = useState<IFbPage[]>([]);
  const [listPagesGrouped, setListPagesGrouped] = useState<IGroupAccount[]>([]);

  const [isLogin, setIsLogin] = useState<boolean>(false);
  const [loading, setLoading] = useState<boolean>(false);
  const [adsAccount, setAdsAccount] = useState<IAdsAccount>();
  const [facebookUser, setFacebookUser] = useState<IFacebookUser>();
  const [refetchLoading, setRefetchLoading] = useState<boolean>(false);
  const controllerRef = useRef<AbortController | null>(null);
  const [isAccountSelected, setIsAccountSelected] = useState<boolean>(false);

  const setDefaultState = () => {
    setIsLogin(false);
    setListPages([]);
    setAdsAccount(undefined);
    setFacebookUser(undefined);
    setLoading(false);
    setRefetchLoading(false);
    deleteStorage(FACEBOOK_STORAGE_KEY);
    controllerRef.current?.abort();
  };

  const mutationListPages = useMutation({
    mutationFn: async () => {
      controllerRef.current?.abort();
      const controller = new AbortController();
      controllerRef.current = controller;
      return await fbOauthApi.getFbAdAccount({ signal: controller.signal });
    },
    onSuccess: (data) => {
      console.log('getFbAdAccount data: ', data);
      if (data.code === 1001) {
        setIsLogin(false);
        setListPages([]);
        setLoading(false);
        return;
      }
      /* eslint-disable @typescript-eslint/no-explicit-any */
      const grouped: IGroupAccount[] = Object.values(
        data.data.reduce((acc: Record<string, IGroupAccount>, item: any) => {
          const businessId = item.business?.id || 'no_business';
          const businessName = item.business?.name || 'No Business';
          if (!acc[businessId]) {
            acc[businessId] = {
              business_id: businessId,
              business_name: businessName,
              accounts: [],
            };
          }
          acc[businessId].accounts.push(item);
          return acc;
        }, {}),
      );

      grouped.sort((a, b): number => {
        const aNoBusiness = a.business_name === 'No Business';
        const bNoBusiness = b.business_name === 'No Business';
        if (aNoBusiness && !bNoBusiness) {
          return 1;
        }
        if (!aNoBusiness && bNoBusiness) {
          return -1;
        }
        return a.business_name.localeCompare(b.business_name);
      });
      setListPagesGrouped(grouped);
      setIsLogin(true);
      setListPages(data.data);
      const adsAccountDefault: IFbPage = data.data.find((account: IFbPage) => account.selected);
      console.log({ adsAccountDefault });
      setAdsAccount({
        ad_account_id: adsAccountDefault.id,
        ad_account_name: adsAccountDefault.name,
      });
      setFacebookUser(data.data);
      setLoading(false);
      setRefetchLoading(false);
    },
    onError: (e) => {
      setDefaultState();
      return e;
    },
  });

  const logoutMutation = useMutation({
    mutationFn: fbOauthApi.logout,
    onSuccess: () => {
      setDefaultState();
    },
  });

  useEffect(() => {
    const isSelected = listPages.some((page) => page.selected);
    console.log('isSelected: ', isSelected);
    console.log('listPages: ', listPages);
    setIsAccountSelected(isSelected);
    if (isSelected) {
      setStorage(FACEBOOK_STORAGE_KEY, String(true));
    }
  }, [listPages]);

  const logout = () => {
    logoutMutation.mutate();
  };

  const updateStateSelected = (id: string) => {
    setListPages((prev) => {
      return prev.map((item) => {
        return {
          ...item,
          selected: item.id === id,
        };
      });
    });

    setStorage(FACEBOOK_STORAGE_KEY, String(true));
  };

  const handleRefetchListPage = () => {
    setRefetchLoading(true);
    mutationListPages.mutate();
  };

  useEffect(() => {
    setLoading(true);
    mutationListPages.mutate();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  return (
    <FbAuthContext.Provider
      value={{
        listPages,
        adsAccount,
        facebookUser,
        listPagesGrouped,
        isLogin,
        loading,
        refetchLoading,
        logout,
        updateStateSelected,
        handleRefetchListPage,
        isAccountSelected,
      }}
    >
      {children}
    </FbAuthContext.Provider>
  );
};

export const useFBContext = (): InitSate => useContext(FbAuthContext);
