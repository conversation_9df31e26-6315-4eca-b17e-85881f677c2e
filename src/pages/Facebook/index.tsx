import { useFBContext } from './context/FbAuthContext';
import EmptyDataView from './views/EmptyDataView';
import Container from './components/Container';
import ConnectFacebookPopup from './components/ConnectFacebokPopup';
import Breadcrumb from '@/components/Breadcrumb';
import HeaderFacebook from '@/pages/Facebook/components/HeaderFacebook';

const FacebookPage = () => {
  const { loading, isAccountSelected, listPages } = useFBContext();
  console.log({ isAccountSelected });
  console.log({ listPages });
  if (loading) {
    return <ConnectFacebookPopup />;
  }

  return (
    <div className="w-full h-full flex flex-col">
      <Breadcrumb />
      {!isAccountSelected ? (
        <>
          <HeaderFacebook />
          <EmptyDataView />
        </>
      ) : (
        <Container />
      )}
    </div>
  );
};

export default FacebookPage;
